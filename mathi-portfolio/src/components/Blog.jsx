import React from 'react'
import { BookO<PERSON>, TrendingUp, Lightbulb, Coffee } from 'lucide-react'

const Blog = () => {
  const upcomingTopics = [
    {
      icon: Lightbulb,
      title: "Tech Innovation Insights",
      description: "Thoughts on emerging technologies and their practical applications in business"
    },
    {
      icon: TrendingUp,
      title: "Startup Journey",
      description: "Lessons learned from building multiple ventures and scaling businesses"
    },
    {
      icon: Coffee,
      title: "Entrepreneurial Life",
      description: "Balancing work, family, and the pursuit of freedom through technology"
    }
  ]

  return (
    <section id="blog" className="section-padding bg-gray-50 dark:bg-gray-800">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Blog & Insights
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Sharing thoughts on technology, entrepreneurship, and the journey to building meaningful ventures
          </p>
        </div>

        {/* Coming Soon Content */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white dark:bg-gray-900 rounded-2xl p-8 md:p-12 text-center border border-gray-100 dark:border-gray-700">
            <div className="w-20 h-20 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-6">
              <BookOpen className="text-blue-600 dark:text-blue-400" size={32} />
            </div>

            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Coming Soon
            </h3>

            <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
              I'm preparing to share insights from my entrepreneurial journey, technical discoveries,
              and thoughts on building technology that creates freedom and wealth. Stay tuned for
              regular updates on innovation, startup life, and practical tech solutions.
            </p>

            {/* Upcoming Topics */}
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              {upcomingTopics.map((topic, index) => (
                <div key={index} className="p-6 bg-gray-50 dark:bg-gray-800 rounded-xl">
                  <div className="w-12 h-12 bg-white dark:bg-gray-700 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <topic.icon className="text-blue-600 dark:text-blue-400" size={24} />
                  </div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{topic.title}</h4>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">{topic.description}</p>
                </div>
              ))}
            </div>

            {/* Newsletter Signup Placeholder */}
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                Get Notified When I Start Writing
              </h4>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Be the first to read my insights on tech entrepreneurship and innovation
              </p>
              <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-2 border border-gray-200 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button className="btn-primary">
                  Notify Me
                </button>
              </div>
            </div>
          </div>

          {/* Sample Topics Preview */}
          <div className="mt-12 grid md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-100 dark:border-gray-700">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
                <span className="text-sm font-medium text-blue-600 dark:text-blue-400">Upcoming Post</span>
              </div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                "Building 7 Ventures: Lessons in Parallel Entrepreneurship"
              </h4>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                How I manage multiple ventures simultaneously and the systems that make it possible...
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-100 dark:border-gray-700">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
                <span className="text-sm font-medium text-blue-600 dark:text-blue-400">Upcoming Post</span>
              </div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                "The ₹50 Crore Goal: Why Ambitious Targets Drive Innovation"
              </h4>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Setting audacious financial goals and how they shape product decisions and business strategy...
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Blog
