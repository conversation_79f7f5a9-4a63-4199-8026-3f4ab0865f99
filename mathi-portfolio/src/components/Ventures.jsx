import React from 'react'
import { ExternalLink, Crown, Search, Truck, ShoppingBag, BarChart3, Zap, CreditCard } from 'lucide-react'

const Ventures = () => {
  const ventures = [
    {
      name: "ChessBrigade",
      description: "Comprehensive chess education platform providing structured learning paths, interactive lessons, and skill development for players of all levels.",
      icon: Crown,
      category: "Education",
      status: "Active",
      color: "from-purple-500 to-purple-600"
    },
    {
      name: "Talebisi",
      description: "Advanced price comparison platform for German e-commerce, helping consumers find the best deals across multiple retailers.",
      icon: Search,
      category: "E-commerce",
      status: "Active",
      color: "from-blue-500 to-blue-600"
    },
    {
      name: "<PERSON>ytoo",
      description: "Innovative crowd shipping platform connecting travelers with people who need items delivered, creating a collaborative logistics network.",
      icon: Truck,
      category: "Logistics",
      status: "Active",
      color: "from-green-500 to-green-600"
    },
    {
      name: "Viveks",
      description: "Modern e-commerce store offering curated products with focus on quality, customer experience, and seamless online shopping.",
      icon: ShoppingBag,
      category: "Retail",
      status: "Active",
      color: "from-orange-500 to-orange-600"
    },
    {
      name: "DataStoryHub",
      description: "AI-powered data storytelling platform that transforms complex data into compelling narratives and actionable insights.",
      icon: BarChart3,
      category: "AI/Analytics",
      status: "Active",
      color: "from-indigo-500 to-indigo-600"
    },
    {
      name: "ConvertEcom",
      description: "Shopify automation tools and apps designed to boost conversion rates, streamline operations, and maximize e-commerce revenue.",
      icon: Zap,
      category: "SaaS",
      status: "Active",
      color: "from-yellow-500 to-yellow-600"
    },
    {
      name: "Early Wage Access System",
      description: "UK-based financial technology solution providing employees with early access to earned wages, improving financial flexibility.",
      icon: CreditCard,
      category: "FinTech",
      status: "Active",
      color: "from-red-500 to-red-600"
    }
  ]

  return (
    <section id="ventures" className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            My Ventures
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A diverse portfolio of technology ventures spanning education, e-commerce, logistics, AI, and fintech
          </p>
        </div>

        {/* Ventures Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {ventures.map((venture, index) => (
            <div key={index} className="group card hover:scale-105 transition-all duration-300">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${venture.color} flex items-center justify-center`}>
                  <venture.icon className="text-white" size={24} />
                </div>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                    {venture.status}
                  </span>
                  <ExternalLink className="text-gray-400 group-hover:text-primary-600 transition-colors" size={16} />
                </div>
              </div>

              {/* Content */}
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-xl font-semibold text-gray-900">{venture.name}</h3>
                </div>
                <span className="text-sm font-medium text-primary-600 bg-primary-50 px-2 py-1 rounded">
                  {venture.category}
                </span>
              </div>

              <p className="text-gray-600 leading-relaxed">
                {venture.description}
              </p>

              {/* Footer */}
              <div className="mt-6 pt-4 border-t border-gray-100">
                <button className="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center gap-1 group-hover:gap-2 transition-all">
                  Learn More
                  <ExternalLink size={14} />
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl p-8 border border-gray-100 max-w-2xl mx-auto">
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">
              Interested in Collaboration?
            </h3>
            <p className="text-gray-600 mb-6">
              I'm always open to discussing new opportunities, partnerships, and innovative projects.
            </p>
            <button 
              onClick={() => document.querySelector('#contact').scrollIntoView({ behavior: 'smooth' })}
              className="btn-primary"
            >
              Get in Touch
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Ventures
