import React from 'react'
import { Brain, Code2, ShoppingCart, Cloud, GraduationCap, CheckCircle } from 'lucide-react'

const Services = () => {
  const services = [
    {
      icon: Brain,
      title: "AI Integration for Developers",
      description: "Help development teams integrate AI capabilities into their applications with practical, scalable solutions.",
      features: [
        "Custom AI model integration",
        "Machine learning pipeline setup",
        "AI-powered feature development",
        "Performance optimization"
      ],
      color: "from-purple-500 to-purple-600"
    },
    {
      icon: Code2,
      title: "Product Building in MERN & Python",
      description: "Full-stack development services using modern technologies to build robust, scalable applications.",
      features: [
        "React & Node.js applications",
        "Python backend development",
        "Database design & optimization",
        "API development & integration"
      ],
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: ShoppingCart,
      title: "Shopify App Development",
      description: "Custom Shopify applications and automation tools to enhance e-commerce operations and boost sales.",
      features: [
        "Custom Shopify apps",
        "Store automation tools",
        "Conversion optimization",
        "Third-party integrations"
      ],
      color: "from-green-500 to-green-600"
    },
    {
      icon: Cloud,
      title: "AWS & Scalable Architecture",
      description: "Cloud infrastructure design and implementation for applications that need to scale efficiently.",
      features: [
        "AWS infrastructure setup",
        "Scalable system design",
        "DevOps & CI/CD pipelines",
        "Performance monitoring"
      ],
      color: "from-orange-500 to-orange-600"
    },
    {
      icon: GraduationCap,
      title: "Technical Training & Bootcamps",
      description: "Comprehensive training programs for individuals and teams looking to upskill in modern technologies.",
      features: [
        "Custom curriculum development",
        "Hands-on project training",
        "Team skill development",
        "Mentorship programs"
      ],
      color: "from-indigo-500 to-indigo-600"
    }
  ]

  const processSteps = [
    {
      step: "01",
      title: "Discovery & Planning",
      description: "Understanding your needs, goals, and technical requirements to create a tailored solution."
    },
    {
      step: "02",
      title: "Strategy & Design",
      description: "Developing a comprehensive strategy and technical architecture for optimal results."
    },
    {
      step: "03",
      title: "Development & Implementation",
      description: "Building and deploying solutions with regular updates and transparent communication."
    },
    {
      step: "04",
      title: "Testing & Optimization",
      description: "Rigorous testing and performance optimization to ensure quality and reliability."
    },
    {
      step: "05",
      title: "Launch & Support",
      description: "Successful deployment with ongoing support and maintenance as needed."
    }
  ]

  return (
    <section id="services" className="section-padding bg-white dark:bg-gray-900">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Services & Consulting
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Helping businesses and developers leverage cutting-edge technology to achieve their goals
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid lg:grid-cols-2 gap-8 mb-20">
          {services.map((service, index) => (
            <div key={index} className="card group hover:scale-105 transition-all duration-300">
              {/* Header */}
              <div className="flex items-start gap-4 mb-6">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${service.color} flex items-center justify-center flex-shrink-0`}>
                  <service.icon className="text-white" size={24} />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">{service.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{service.description}</p>
                </div>
              </div>

              {/* Features */}
              <div className="space-y-3">
                {service.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center gap-3">
                    <CheckCircle className="text-green-500 flex-shrink-0" size={16} />
                    <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Process Section */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 md:p-12">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              My Process
            </h3>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              A proven methodology that ensures successful project delivery and client satisfaction
            </p>
          </div>

          <div className="grid md:grid-cols-5 gap-6">
            {processSteps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-blue-600 dark:bg-blue-500 text-white rounded-full flex items-center justify-center font-bold text-sm mx-auto mb-4">
                  {step.step}
                </div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{step.title}</h4>
                <p className="text-gray-600 dark:text-gray-300 text-sm">{step.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-500 dark:to-blue-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Start Your Project?
            </h3>
            <p className="text-blue-100 dark:text-blue-200 mb-6 max-w-2xl mx-auto">
              Let's discuss how I can help you achieve your technical goals and build something amazing together.
            </p>
            <button
              onClick={() => document.querySelector('#contact').scrollIntoView({ behavior: 'smooth' })}
              className="bg-white text-blue-600 hover:bg-gray-100 dark:bg-gray-100 dark:text-blue-600 dark:hover:bg-gray-200 font-medium py-3 px-8 rounded-lg transition-colors duration-200 inline-flex items-center gap-2"
            >
              Start a Conversation
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Services
