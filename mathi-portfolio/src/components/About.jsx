import React from 'react'
import { Award, Users, TrendingUp, Heart } from 'lucide-react'

const About = () => {
  const highlights = [
    {
      icon: Award,
      title: "Engineering Excellence",
      description: "Strong foundation in software engineering with expertise across multiple technology stacks"
    },
    {
      icon: TrendingUp,
      title: "Business Vision",
      description: "Ambitious goal of building wealth (₹50 crore) through meaningful technology ventures"
    },
    {
      icon: Users,
      title: "Team Leadership",
      description: "Leading Yalabs Solutions and building products that make a real difference"
    },
    {
      icon: Heart,
      title: "Life Balance",
      description: "Pursuing freedom and luxury lifestyle while maintaining strong family values"
    }
  ]

  return (
    <section id="about" className="section-padding bg-white dark:bg-gray-900">
      <div className="container-custom">
        <div className="max-w-4xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              About Me
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              From engineer to entrepreneur, I'm on a mission to build technology that creates freedom and wealth
            </p>
          </div>

          {/* Main Content */}
          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            {/* Text Content */}
            <div className="space-y-6">
              <div>
                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                  Engineer Turned Entrepreneur
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  My journey began in engineering, where I developed a deep appreciation for solving complex problems
                  through technology. This foundation led me to entrepreneurship, where I could combine technical
                  expertise with business vision to create meaningful solutions.
                </p>
              </div>

              <div>
                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                  Vision for Freedom & Impact
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  I believe technology should create freedom—both financial and personal. My goal is to build
                  ventures that not only generate substantial wealth (targeting ₹50 crore) but also provide
                  the lifestyle and freedom to enjoy life with family while making a positive impact.
                </p>
              </div>

              <div>
                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                  Philosophy: Simplicity & Execution
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  I focus on clarity over complexity, execution over endless planning. Every project I undertake
                  is driven by the principle of creating real value through simple, effective solutions that
                  solve genuine problems.
                </p>
              </div>
            </div>

            {/* Visual Element */}
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 rounded-2xl p-8 text-center">
                <div className="text-6xl font-bold text-blue-600 dark:text-blue-400 mb-2">₹50Cr</div>
                <div className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Wealth Target</div>
                <div className="text-gray-600 dark:text-gray-400">
                  Building sustainable ventures that create lasting value and financial freedom
                </div>
              </div>
            </div>
          </div>

          {/* Highlights Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {highlights.map((item, index) => (
              <div key={index} className="card text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <item.icon className="text-blue-600 dark:text-blue-400" size={24} />
                </div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{item.title}</h4>
                <p className="text-gray-600 dark:text-gray-300 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
