import React from 'react'
import { <PERSON>R<PERSON>, <PERSON>, Rocket, Target } from 'lucide-react'

const Hero = () => {
  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="hero" className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 pt-16">
      <div className="container-custom">
        <div className="text-center max-w-4xl mx-auto">
          {/* Main Content */}
          <div className="animate-fade-in">
            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6">
              <span className="block"><PERSON><PERSON></span>
              <span className="text-gradient text-4xl md:text-5xl font-medium">
                Building ideas into reality
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Tech entrepreneur, consultant, and educator. Founder of Yalabs Solutions Private Limited.
              I chase freedom and results. Tech is my playground.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <button
                onClick={() => scrollToSection('#ventures')}
                className="btn-primary text-lg"
              >
                Explore My Work
                <ArrowRight size={20} />
              </button>
              <button
                onClick={() => scrollToSection('#contact')}
                className="btn-secondary text-lg"
              >
                Get in Touch
              </button>
            </div>
          </div>

          {/* Feature Icons */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 animate-slide-up">
            <div className="flex flex-col items-center p-6 rounded-xl bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                <Code className="text-blue-600 dark:text-blue-400" size={24} />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Tech Innovation</h3>
              <p className="text-gray-600 dark:text-gray-300 text-center">Building cutting-edge solutions with modern technologies</p>
            </div>

            <div className="flex flex-col items-center p-6 rounded-xl bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                <Rocket className="text-blue-600 dark:text-blue-400" size={24} />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Entrepreneurship</h3>
              <p className="text-gray-600 dark:text-gray-300 text-center">Launching ventures that solve real-world problems</p>
            </div>

            <div className="flex flex-col items-center p-6 rounded-xl bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                <Target className="text-blue-600 dark:text-blue-400" size={24} />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Results Driven</h3>
              <p className="text-gray-600 dark:text-gray-300 text-center">Focused on execution and meaningful impact</p>
            </div>
          </div>

          {/* Quote */}
          <div className="mt-16 p-8 bg-white/30 dark:bg-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-100 dark:border-gray-700">
            <blockquote className="text-2xl md:text-3xl font-medium text-gray-800 dark:text-gray-200 italic">
              "I chase freedom and results. Tech is my playground."
            </blockquote>
            <cite className="block mt-4 text-gray-600 dark:text-gray-400 font-medium">— Mathi Arasan</cite>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
