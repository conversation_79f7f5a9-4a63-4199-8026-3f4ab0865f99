@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Custom styles for Tailwind v4 */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  color: #1f2937;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.dark body {
  color: #f3f4f6;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 inline-flex items-center gap-2;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 font-medium py-3 px-6 rounded-lg transition-colors duration-200 inline-flex items-center gap-2;
}

.section-padding {
  @apply py-16 md:py-24;
}

.container-custom {
  @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8;
}

.text-gradient {
  @apply bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent;
}

.card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-lg transition-all duration-200;
}