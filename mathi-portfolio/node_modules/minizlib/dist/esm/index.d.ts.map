{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAA;AAC/B,OAAO,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAA;AACnC,OAAO,KAAK,QAAQ,MAAM,MAAM,CAAA;AAEhC,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAA;AAY1C,QAAA,MAAM,WAAW,eAAwB,CAAA;AAEzC,qBAAa,SAAU,SAAQ,KAAK;IAClC,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,KAAK,CAAC,EAAE,MAAM,CAAA;gBACF,GAAG,EAAE,MAAM,CAAC,cAAc,GAAG,KAAK;IAW9C,IAAI,IAAI,WAEP;CACF;AAMD,QAAA,MAAM,UAAU,eAAsB,CAAA;AAEtC,MAAM,MAAM,kBAAkB,GAAG,QAAQ,CAAC,cAAc,GAAG;IACzD,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAA;CACtB,CAAA;AAED,MAAM,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG;IACxE,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,aAAa,CAAC,EAAE,MAAM,CAAA;CACvB,CAAA;AACD,MAAM,MAAM,QAAQ,GAChB,MAAM,GACN,QAAQ,GACR,SAAS,GACT,SAAS,GACT,YAAY,GACZ,YAAY,GACZ,OAAO,CAAA;AACX,MAAM,MAAM,UAAU,GAClB,QAAQ,CAAC,IAAI,GACb,QAAQ,CAAC,MAAM,GACf,QAAQ,CAAC,OAAO,GAChB,QAAQ,CAAC,OAAO,GAChB,QAAQ,CAAC,UAAU,GACnB,QAAQ,CAAC,UAAU,CAAA;AACvB,MAAM,MAAM,UAAU,GAAG,gBAAgB,GAAG,kBAAkB,CAAA;AAE9D,uBAAe,QAAS,SAAQ,QAAQ,CAAC,MAAM,EAAE,kBAAkB,CAAC;;IASlE,IAAI,QAAQ,YAEX;IACD,IAAI,MAAM,2BAET;IAED,IAAI,SAAS,WAEZ;gBAGW,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,GAAG,UAAU;IAuC9D,KAAK;IAQL,KAAK;IAQL,KAAK,CAAC,SAAS,CAAC,EAAE,MAAM;IAQxB,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,IAAI;IAC1B,GAAG,CAAC,KAAK,EAAE,kBAAkB,EAAE,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,IAAI;IACrD,GAAG,CACD,KAAK,EAAE,kBAAkB,EACzB,QAAQ,CAAC,EAAE,QAAQ,CAAC,QAAQ,EAC5B,EAAE,CAAC,EAAE,MAAM,IAAI,GACd,IAAI;IA0BP,IAAI,KAAK,YAER;IAGD,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,MAAM,GAAG;QAAE,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAA;KAAE;IAItD,KAAK,CAAC,KAAK,EAAE,kBAAkB,EAAE,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,OAAO;IAC1D,KAAK,CACH,KAAK,EAAE,kBAAkB,EACzB,QAAQ,CAAC,EAAE,QAAQ,CAAC,QAAQ,EAC5B,EAAE,CAAC,EAAE,MAAM,IAAI,GACd,OAAO;CAqFX;AAED,MAAM,MAAM,WAAW,GAAG,eAAe,GAAG;IAC1C,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,QAAQ,CAAC,EAAE,MAAM,CAAA;CAClB,CAAA;AAED,qBAAa,IAAK,SAAQ,QAAQ;;gBAIpB,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ;IAY7C,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;CAiDvC;AAGD,qBAAa,OAAQ,SAAQ,IAAI;gBACnB,IAAI,EAAE,WAAW;CAG9B;AAED,qBAAa,OAAQ,SAAQ,IAAI;gBACnB,IAAI,EAAE,WAAW;CAG9B;AAGD,MAAM,MAAM,WAAW,GAAG,WAAW,GAAG;IAAE,QAAQ,CAAC,EAAE,OAAO,CAAA;CAAE,CAAA;AAC9D,qBAAa,IAAK,SAAQ,IAAI;;gBAEhB,IAAI,EAAE,WAAW;IAK7B,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,MAAM,GAAG;QAAE,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAA;KAAE;CASvD;AAED,qBAAa,MAAO,SAAQ,IAAI;gBAClB,IAAI,EAAE,WAAW;CAG9B;AAGD,qBAAa,UAAW,SAAQ,IAAI;gBACtB,IAAI,EAAE,WAAW;CAG9B;AAED,qBAAa,UAAW,SAAQ,IAAI;gBACtB,IAAI,EAAE,WAAW;CAG9B;AAGD,qBAAa,KAAM,SAAQ,IAAI;gBACjB,IAAI,EAAE,WAAW;CAG9B;AAED,qBAAa,MAAO,SAAQ,QAAQ;gBACtB,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU;CAShD;AAED,qBAAa,cAAe,SAAQ,MAAM;gBAC5B,IAAI,EAAE,WAAW;CAG9B;AAED,qBAAa,gBAAiB,SAAQ,MAAM;gBAC9B,IAAI,EAAE,WAAW;CAG9B"}