{"version": 3, "file": "large-numbers.js", "sourceRoot": "", "sources": ["../../src/large-numbers.ts"], "names": [], "mappings": "AAAA,oEAAoE;AACpE,4CAA4C;AAE5C,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE;IACjD,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/B,0EAA0E;QAC1E,aAAa;QACb,MAAM,KAAK,CACT,+DAA+D,CAChE,CAAA;IACH,CAAC;SAAM,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;QACnB,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAC1B,CAAC;SAAM,CAAC;QACN,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAC1B,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAM,cAAc,GAAG,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE;IAClD,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;IAEb,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAA;QACvB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,CAAA;IAC/B,CAAC;AACH,CAAC,CAAA;AAED,MAAM,cAAc,GAAG,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE;IAClD,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;IACb,IAAI,OAAO,GAAG,KAAK,CAAA;IACnB,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAA;IACd,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAA;QACrB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,CAAA;QAC7B,IAAI,OAAO,EAAE,CAAC;YACZ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC7B,CAAC;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACtB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;QAChB,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,IAAI,CAAA;YACd,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC7B,CAAC;IACH,CAAC;AACH,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,GAAW,EAAE,EAAE;IACnC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;IAClB,MAAM,KAAK,GACT,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YAC1B,CAAC,CAAC,IAAI,CAAA;IACR,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QACnB,MAAM,KAAK,CAAC,0BAA0B,CAAC,CAAA;IACzC,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;QACjC,0EAA0E;QAC1E,aAAa;QACb,MAAM,KAAK,CACT,wDAAwD,CACzD,CAAA;IACH,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AAED,MAAM,IAAI,GAAG,CAAC,GAAW,EAAE,EAAE;IAC3B,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAA;IACpB,IAAI,GAAG,GAAG,CAAC,CAAA;IACX,IAAI,OAAO,GAAG,KAAK,CAAA;IACnB,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAClC,IAAI,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QACzB,IAAI,CAAC,CAAA;QACL,IAAI,OAAO,EAAE,CAAC;YACZ,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;QACpB,CAAC;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACtB,CAAC,GAAG,IAAI,CAAA;QACV,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,IAAI,CAAA;YACd,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;QACpB,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACZ,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;QACvC,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAM,GAAG,GAAG,CAAC,GAAW,EAAE,EAAE;IAC1B,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAA;IACpB,IAAI,GAAG,GAAG,CAAC,CAAA;IACX,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAClC,IAAI,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QACzB,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACf,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;QAC1C,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAA;AAEvD,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAA", "sourcesContent": ["// Tar can encode large and negative numbers using a leading byte of\n// 0xff for negative, and 0x80 for positive.\n\nexport const encode = (num: number, buf: Buffer) => {\n  if (!Number.isSafeInteger(num)) {\n    // The number is so large that javascript cannot represent it with integer\n    // precision.\n    throw Error(\n      'cannot encode number outside of javascript safe integer range',\n    )\n  } else if (num < 0) {\n    encodeNegative(num, buf)\n  } else {\n    encodePositive(num, buf)\n  }\n  return buf\n}\n\nconst encodePositive = (num: number, buf: Buffer) => {\n  buf[0] = 0x80\n\n  for (var i = buf.length; i > 1; i--) {\n    buf[i - 1] = num & 0xff\n    num = Math.floor(num / 0x100)\n  }\n}\n\nconst encodeNegative = (num: number, buf: Buffer) => {\n  buf[0] = 0xff\n  var flipped = false\n  num = num * -1\n  for (var i = buf.length; i > 1; i--) {\n    var byte = num & 0xff\n    num = Math.floor(num / 0x100)\n    if (flipped) {\n      buf[i - 1] = onesComp(byte)\n    } else if (byte === 0) {\n      buf[i - 1] = 0\n    } else {\n      flipped = true\n      buf[i - 1] = twosComp(byte)\n    }\n  }\n}\n\nexport const parse = (buf: Buffer) => {\n  const pre = buf[0]\n  const value =\n    pre === 0x80 ? pos(buf.subarray(1, buf.length))\n    : pre === 0xff ? twos(buf)\n    : null\n  if (value === null) {\n    throw Error('invalid base256 encoding')\n  }\n\n  if (!Number.isSafeInteger(value)) {\n    // The number is so large that javascript cannot represent it with integer\n    // precision.\n    throw Error(\n      'parsed number outside of javascript safe integer range',\n    )\n  }\n\n  return value\n}\n\nconst twos = (buf: Buffer) => {\n  var len = buf.length\n  var sum = 0\n  var flipped = false\n  for (var i = len - 1; i > -1; i--) {\n    var byte = Number(buf[i])\n    var f\n    if (flipped) {\n      f = onesComp(byte)\n    } else if (byte === 0) {\n      f = byte\n    } else {\n      flipped = true\n      f = twosComp(byte)\n    }\n    if (f !== 0) {\n      sum -= f * Math.pow(256, len - i - 1)\n    }\n  }\n  return sum\n}\n\nconst pos = (buf: Buffer) => {\n  var len = buf.length\n  var sum = 0\n  for (var i = len - 1; i > -1; i--) {\n    var byte = Number(buf[i])\n    if (byte !== 0) {\n      sum += byte * Math.pow(256, len - i - 1)\n    }\n  }\n  return sum\n}\n\nconst onesComp = (byte: number) => (0xff ^ byte) & 0xff\n\nconst twosComp = (byte: number) => ((0xff ^ byte) + 1) & 0xff\n"]}