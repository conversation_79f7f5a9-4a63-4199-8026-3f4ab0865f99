{"version": 3, "file": "mode-fix.js", "sourceRoot": "", "sources": ["../../src/mode-fix.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,OAAO,GAAG,CACrB,IAAY,EACZ,KAAc,EACd,QAAiB,EACjB,EAAE;IACF,IAAI,IAAI,MAAM,CAAA;IAEd,qDAAqD;IACrD,qDAAqD;IACrD,mDAAmD;IACnD,uDAAuD;IACvD,qDAAqD;IACrD,IAAI,QAAQ,EAAE,CAAC;QACb,IAAI,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAA;IAC/B,CAAC;IAED,qDAAqD;IACrD,IAAI,KAAK,EAAE,CAAC;QACV,IAAI,IAAI,GAAG,KAAK,EAAE,CAAC;YACjB,IAAI,IAAI,KAAK,CAAA;QACf,CAAC;QACD,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;YAChB,IAAI,IAAI,IAAI,CAAA;QACd,CAAC;QACD,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;YACf,IAAI,IAAI,GAAG,CAAA;QACb,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC,CAAA", "sourcesContent": ["export const modeFix = (\n  mode: number,\n  isDir: boolean,\n  portable: boolean,\n) => {\n  mode &= 0o7777\n\n  // in portable mode, use the minimum reasonable umask\n  // if this system creates files with 0o664 by default\n  // (as some linux distros do), then we'll write the\n  // archive with 0o644 instead.  Also, don't ever create\n  // a file that is not readable/writable by the owner.\n  if (portable) {\n    mode = (mode | 0o600) & ~0o22\n  }\n\n  // if dirs are readable, then they should be listable\n  if (isDir) {\n    if (mode & 0o400) {\n      mode |= 0o100\n    }\n    if (mode & 0o40) {\n      mode |= 0o10\n    }\n    if (mode & 0o4) {\n      mode |= 0o1\n    }\n  }\n  return mode\n}\n"]}